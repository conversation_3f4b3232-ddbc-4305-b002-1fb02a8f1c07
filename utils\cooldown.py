"""
币安马丁格尔策略冷却期管理模块
"""
import time
import json
from utils.utility import get_file_path, load_json, save_json


class CooldownManager:
    """
    管理代币的冷却期，防止在止盈后短时间内再次买入
    """
    def __init__(self, file_name="symbol_cooldown.json"):
        self.file_name = file_name
        self.cooldown_data = {}  # {symbol: {"timestamp": 时间戳, "reason": "止盈/止损/超时"}}
        self.read_data()

    def read_data(self):
        """读取冷却数据"""
        filepath = get_file_path(self.file_name)
        data = load_json(filepath)
        if data:
            self.cooldown_data = data
        else:
            self.cooldown_data = {}

    def save_data(self):
        """保存冷却数据"""
        filepath = get_file_path(self.file_name)
        save_json(filepath, self.cooldown_data)

    def add_cooldown(self, symbol, reason="profit"):
        """
        添加冷却期
        :param symbol: 交易对符号
        :param reason: 冷却原因，默认为止盈(profit)
        """
        self.cooldown_data[symbol] = {
            "timestamp": int(time.time()),
            "reason": reason
        }
        self.save_data()

    def is_in_cooldown(self, symbol, hours=72):
        """
        检查交易对是否在冷却期内
        :param symbol: 交易对符号
        :param hours: 冷却时间(小时)
        :return: 如果在冷却期内返回True，否则返回False
        """
        if symbol not in self.cooldown_data:
            return False

        cooldown_time = self.cooldown_data[symbol]["timestamp"]
        current_time = int(time.time())
        
        # 检查是否已经过了冷却期
        if current_time - cooldown_time >= hours * 3600:
            # 冷却期已过，可以删除记录
            del self.cooldown_data[symbol]
            self.save_data()
            return False
            
        return True
    
    def get_remaining_cooldown_time(self, symbol, hours=72):
        """
        获取剩余冷却时间（小时）
        :param symbol: 交易对符号
        :param hours: 冷却总时间(小时)
        :return: 剩余冷却时间(小时)，如果不在冷却期则返回0
        """
        if symbol not in self.cooldown_data:
            return 0
            
        cooldown_time = self.cooldown_data[symbol]["timestamp"]
        current_time = int(time.time())
        elapsed_seconds = current_time - cooldown_time
        
        if elapsed_seconds >= hours * 3600:
            return 0
            
        remaining_hours = (hours * 3600 - elapsed_seconds) / 3600
        return remaining_hours
    
    def format_remaining_time(self, symbol, hours=72):
        """
        格式化剩余冷却时间
        :param symbol: 交易对符号
        :param hours: 冷却总时间(小时)
        :return: 格式化的时间字符串，如"1天2小时30分钟"
        """
        if symbol not in self.cooldown_data:
            return "0小时"
            
        cooldown_time = self.cooldown_data[symbol]["timestamp"]
        current_time = int(time.time())
        elapsed_seconds = current_time - cooldown_time
        
        if elapsed_seconds >= hours * 3600:
            return "0小时"
            
        remaining_seconds = hours * 3600 - elapsed_seconds
        
        # 转换为天、小时、分钟
        days = remaining_seconds // 86400
        remaining_seconds %= 86400
        hours_remaining = remaining_seconds // 3600
        remaining_seconds %= 3600
        minutes = remaining_seconds // 60
        
        # 构建可读字符串
        time_str = ""
        if days > 0:
            time_str += f"{days}天"
        if hours_remaining > 0 or days > 0:
            time_str += f"{hours_remaining}小时"
        time_str += f"{minutes}分钟"
        
        return time_str
    
    def get_cooldown_symbols(self):
        """
        获取所有在冷却期的交易对
        :return: 冷却中的交易对列表
        """
        return list(self.cooldown_data.keys()) 