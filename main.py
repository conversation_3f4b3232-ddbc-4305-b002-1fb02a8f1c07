"""
    币安推荐码:  返佣10%
    https://www.binancezh.pro/cn/register?ref=AIR1GC70

    币安合约推荐码: 返佣10%
    https://www.binancezh.com/cn/futures/ref/51bitquant

    if you don't have a binance account, you can use the invitation link to register one:
    https://www.binancezh.com/cn/futures/ref/51bitquant

    or use the inviation code: 51bitquant

    风险提示: 网格交易在单边行情的时候，会承受比较大的风险，请你了解整个代码的逻辑，然后再使用。
    RISK NOTE: Grid trading will endure great risk at trend market, please check the code before use it. USE AT YOUR OWN RISK.

"""

import time
import logging
import traceback
import sys
import random
from trader.binance_spot_trader import BinanceSpotTrader
from trader.binance_future_trader import BinanceFutureTrader
from utils import config
from apscheduler.schedulers.background import BackgroundScheduler
from utils.cooldown import CooldownManager

format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.INFO, format=format, filename='log.txt')
logging.getLogger("apscheduler.scheduler").setLevel(logging.WARNING)
logging.getLogger("apscheduler.executors.default").setLevel(logging.WARNING)

logger = logging.getLogger('binance')
from typing import Union
from gateway.binance_future import Interval
import numpy as np
import pandas as pd
from datetime import datetime

pd.set_option('expand_frame_repr', False)

from utils.config import signal_data


def get_data(trader: Union[BinanceFutureTrader, BinanceSpotTrader]):
    # traders.symbols is a dict data structure.
    symbols = trader.symbols_dict.keys()

    signals = []

    # we calculate the signal here.
    if len(config.allowed_lists) > 0:
        symbols = config.allowed_lists

    # 添加进度统计
    total_symbols = len(list(symbols))
    processed = 0
    errors = 0
    
    for symbol in symbols:
        try:
            processed += 1
            
            if len(config.blocked_lists) > 0:
                if symbol.upper() in config.blocked_lists:
                    continue

            # 打印进度
            print(f"处理交易对 {symbol} ({processed}/{total_symbols})")
            
            # 添加随机等待时间，避免频繁请求
            if processed > 1:  # 第一个不等待
                wait_time = random.uniform(0.5, 2.0)  # 随机等待0.5到2秒
                print(f"等待 {wait_time:.2f} 秒后请求下一个交易对数据...")
                time.sleep(wait_time)
            
            klines = trader.get_klines(symbol=symbol.upper(), interval=Interval.HOUR_1, limit=100)
            if len(klines) > 0:
                df = pd.DataFrame(klines, dtype=np.float64,
                                  columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'close_time', 'turnover', 'a2',
                                           'a3', 'a4', 'a5'])
                df = df[['open_time', 'open', 'high', 'low', 'close', 'volume', 'turnover']]
                df.set_index('open_time', inplace=True)
                df.index = pd.to_datetime(df.index, unit='ms') + pd.Timedelta(hours=8)

                df_4hour = df.resample(rule='4H').agg({'open': 'first',
                                            'high': 'max',
                                            'low': 'min',
                                            'close': 'last',
                                            'volume': 'sum',
                                            'turnover': 'sum'
                                            })

                # print(df)

                # calculate the pair's price change is one hour. you can modify the code below.
                pct = df['close'] / df['open'] - 1
                pct_4h = df_4hour['close']/df_4hour['open'] - 1

                value = {'pct': pct.iloc[-1], 'pct_4h':pct_4h.iloc[-1] , 'symbol': symbol, 'hour_turnover': df['turnover'].iloc[-1]}


                # calculate your signal here.
                if value['pct'] >= config.pump_pct or value['pct_4h'] >= config.pump_pct_4h:
                    # the signal 1 mean buy signal.
                    value['signal'] = 1
                elif value['pct'] <= -config.pump_pct or value['pct_4h'] <= -config.pump_pct_4h:
                    value['signal'] = -1
                else:
                    value['signal'] = 0

                signals.append(value)
        except Exception as e:
            errors += 1
            error_msg = f"处理交易对 {symbol} 时出错: {str(e)}"
            print(error_msg)
            logger.error(error_msg)
    
    print(f"所有交易对处理完成。总数: {total_symbols}, 成功: {processed-errors}, 失败: {errors}")

    if signals:  # 确保有信号再进行排序和通知
        signals.sort(key=lambda x: x['pct'], reverse=True)
        signal_data['id'] = signal_data['id'] + 1
        signal_data['time'] = datetime.now()
        signal_data['signals'] = signals
        print(signal_data)
        
        # 发送钉钉策略状态通知
        send_strategy_status_notification(trader)
    else:
        error_msg = "警告：没有获取到任何交易信号!"
        print(error_msg)
        logger.warning(error_msg)


def send_strategy_status_notification(trader):
    """
    发送钉钉策略状态通知
    :param trader: 交易对象
    """
    # 如果钉钉通知未启用，直接返回
    if not config.dingding_notify.get('enabled', False) or not config.dingding_notifier:
        return
        
    current_time = time.time()
    # 检查是否需要发送通知（根据通知间隔）
    notify_interval = config.dingding_notify.get('notify_interval', 3600)
    if current_time - config.last_notify_time < notify_interval:
        return
        
    try:
        # 获取账户余额信息
        balance = 0
        if hasattr(trader, 'http_client') and hasattr(trader.http_client, 'get_account'):
            account_info = trader.http_client.get_account()
            if config.platform == 'binance_spot':
                # 现货账户余额
                for asset in account_info.get('balances', []):
                    if asset.get('asset') == 'USDT':
                        balance = float(asset.get('free', 0)) + float(asset.get('locked', 0))
                        break
            else:
                # 合约账户余额
                balance = float(account_info.get('totalWalletBalance', 0))
                
        # 获取持仓信息
        positions = trader.positions.positions if hasattr(trader, 'positions') else {}
        total_profit = trader.positions.total_profit if hasattr(trader, 'positions') else 0
                
        # 发送通知
        config.dingding_notifier.send_strategy_status(
            positions=positions,
            signals=signal_data.get('signals', []),
            total_profit=total_profit,
            balance=balance
        )
        
        # 更新最后通知时间
        config.last_notify_time = current_time
        
    except Exception as e:
        error_msg = f"发送策略状态通知异常: {str(e)}"
        logger.error(error_msg)
        if config.dingding_notify.get('notify_on_error', True) and config.dingding_notifier:
            config.dingding_notifier.send_error_notification(
                error_msg=error_msg,
                error_trace=traceback.format_exc()
            )


def handle_error(e):
    """
    处理错误并发送通知
    :param e: 异常对象
    """
    error_msg = f"策略运行异常: {str(e)}"
    logger.error(error_msg)
    
    if config.dingding_notify.get('notify_on_error', True) and config.dingding_notifier:
        config.dingding_notifier.send_error_notification(
            error_msg=error_msg,
            error_trace=traceback.format_exc()
        )


if __name__ == '__main__':

    try:
        config.loads('./config.json')
        print(config.blocked_lists)

        # 初始化冷却管理器
        cooldown_manager = CooldownManager()
        
        if config.platform == 'binance_spot':
            # if you want to trade spot, set the platform to 'binance_spot',  else will trade Binance Future(USDT Base)
            # 如果你交易的是币安现货，就设置config.platform 为 'binance_spot'，否则就交易的是币安永续合约(USDT)
            trader = BinanceSpotTrader()
        else:
            trader = BinanceFutureTrader()


        trader.get_exchange_info()
        
        # 将冷却管理器传递给交易者
        trader.set_cooldown_manager(cooldown_manager)
        
        get_data(trader)  # for testing

        scheduler = BackgroundScheduler()
        scheduler.add_job(get_data, trigger='cron', hour='*/1', args=(trader,))
        scheduler.start()

        # 发送启动通知
        if config.dingding_notify.get('enabled', False) and config.dingding_notifier:
            config.dingding_notifier.send_message(
                f"### 策略启动通知\n\n"
                f"- 策略已启动\n"
                f"- 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"- 平台: {config.platform}\n"
                f"- 最大交易对数量: {config.max_pairs}\n",
                "markdown"
            )

        while True:
            try:
                time.sleep(10)
                trader.start()
            except Exception as e:
                handle_error(e)
                time.sleep(60)  # 出错后暂停一分钟再继续
    except Exception as e:
        handle_error(e)
        sys.exit(1)

"""
策略逻辑: 

1. 每1个小时会挑选出前几个波动率最大的交易对(假设交易的是四个交易对).
2. 然后根据设置的参数进行下单(假设有两个仓位,那么波动率最大的两个，且他们过去一段时间是暴涨过的)
3. 然后让他们执行马丁策略.


Martingle trading strategy: 

1. select the top trading pairs with the highest volatility every hour (assuming four trading pairs are traded, you can config in the config.json file)

2. Then place an order based on the setting parameters

3. Then have them execute the Martin strategy

"""
