import requests
import json
import traceback
import logging
import time
import hmac
import hashlib
import base64
import urllib.parse
from datetime import datetime

class DingdingNotifier:
    def __init__(self, webhook_url, secret=None):
        """
        初始化钉钉通知器
        :param webhook_url: 钉钉机器人webhook地址
        :param secret: 钉钉机器人安全设置的secret（如果有）
        """
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = logging.getLogger('dingding_notifier')
        self.last_notify_time = 0
    
    def _get_signed_url(self):
        """
        计算签名并返回带签名的URL
        """
        if not self.secret:
            return self.webhook_url
            
        timestamp = str(round(time.time() * 1000))
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode(), 
            string_to_sign.encode(), 
            digestmod=hashlib.sha256
        ).digest()
        
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        webhook_url = f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
        return webhook_url
    
    def send_message(self, message, msg_type="text"):
        """
        发送钉钉消息
        :param message: 消息内容，可以是字符串或字典
        :param msg_type: 消息类型，支持text、markdown
        """
        try:
            if msg_type == "text":
                data = {
                    "msgtype": "text",
                    "text": {"content": message}
                }
            elif msg_type == "markdown":
                if isinstance(message, dict):
                    title = message.get("title", "策略状态通知")
                    content = message.get("content", "")
                else:
                    title = "策略状态通知"
                    content = message
                
                data = {
                    "msgtype": "markdown",
                    "markdown": {
                        "title": title,
                        "text": content
                    }
                }
            
            headers = {'Content-Type': 'application/json'}
            webhook_url = self._get_signed_url()
            
            response = requests.post(webhook_url, data=json.dumps(data), headers=headers)
            
            if response.status_code == 200:
                resp_data = response.json()
                if resp_data.get('errcode') == 0:
                    self.logger.info(f"钉钉通知发送成功")
                    return True
                else:
                    self.logger.error(f"钉钉通知发送失败: {resp_data}")
                    return False
            else:
                self.logger.error(f"钉钉通知发送失败: {response.status_code}, {response.text}")
                return False
        except Exception as e:
            self.logger.error(f"钉钉通知异常: {str(e)}\n{traceback.format_exc()}")
            return False

    def send_strategy_status(self, positions, signals, total_profit=0, balance=0):
        """
        发送策略状态通知
        :param positions: 当前持仓信息
        :param signals: 当前信号列表
        :param total_profit: 总收益
        :param balance: 账户余额
        """
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建Markdown消息
        md_content = f"### 策略状态通知 - {now}\n\n"
        
        # 账户信息
        md_content += f"#### 账户信息\n"
        md_content += f"- 账户余额: {balance} USDT\n"
        md_content += f"- 总收益: {total_profit} USDT\n\n"
        
        # 持仓信息
        md_content += f"#### 当前持仓 ({len(positions)})\n"
        if positions:
            for symbol, pos_data in positions.items():
                avg_price = pos_data.get('avg_price', 0)
                pos = pos_data.get('pos', 0)
                entry_price = pos_data.get('last_entry_price', 0)
                max_price = pos_data.get('profit_max_price', 0)
                increase_count = pos_data.get('current_increase_pos_count', 0)
                
                # 获取持仓时间信息
                entry_time = pos_data.get('entry_time', 0)
                holding_time = ""
                if entry_time > 0:
                    # 计算持仓时间（秒）
                    holding_seconds = int(time.time()) - entry_time
                    # 转换为天、小时、分钟
                    days = holding_seconds // 86400
                    holding_seconds %= 86400
                    hours = holding_seconds // 3600
                    holding_seconds %= 3600
                    minutes = holding_seconds // 60
                    
                    # 构建可读字符串
                    if days > 0:
                        holding_time = f"{days}天"
                    if hours > 0 or days > 0:
                        holding_time += f"{hours}小时"
                    holding_time += f"{minutes}分钟"
                else:
                    holding_time = "未知"
                
                md_content += f"- **{symbol}**:\n"
                md_content += f"  - 持仓量: {pos}\n"
                md_content += f"  - 均价: {avg_price:.8f}\n"
                md_content += f"  - 最近入场价: {entry_price:.8f}\n"
                md_content += f"  - 最高价: {max_price:.8f}\n"
                md_content += f"  - 加仓次数: {increase_count}\n"
                md_content += f"  - 持仓时间: {holding_time}\n\n"
        else:
            md_content += "暂无持仓\n\n"
            
        # 添加冷却中的交易对信息
        try:
            from utils.cooldown import CooldownManager
            cooldown_manager = CooldownManager()
            cooldown_symbols = cooldown_manager.get_cooldown_symbols()
            
            md_content += f"#### 冷却中交易对 ({len(cooldown_symbols)})\n"
            if cooldown_symbols:
                for symbol in cooldown_symbols:
                    remaining_time = cooldown_manager.format_remaining_time(symbol, config.profit_cooldown_hours)
                    reason = cooldown_manager.cooldown_data.get(symbol, {}).get('reason', '未知')
                    reason_text = "止盈" if reason == "profit" else reason
                    
                    md_content += f"- **{symbol}**: 剩余 {remaining_time}，原因: {reason_text}\n"
            else:
                md_content += "暂无冷却中的交易对\n\n"
        except Exception as e:
            self.logger.error(f"获取冷却信息失败: {str(e)}")
        
        # 信号信息
        md_content += f"#### 最新信号 (Top 5)\n"
        if signals:
            top_signals = sorted(signals, key=lambda x: x.get('pct', 0), reverse=True)[:5]
            for signal in top_signals:
                symbol = signal.get('symbol', '')
                pct = signal.get('pct', 0)
                pct_4h = signal.get('pct_4h', 0)
                signal_value = signal.get('signal', 0)
                
                signal_text = "买入" if signal_value == 1 else "卖出" if signal_value == -1 else "观望"
                
                # 检查信号是否在冷却期
                in_cooldown = False
                cooldown_info = ""
                try:
                    from utils.cooldown import CooldownManager
                    cooldown_manager = CooldownManager()
                    if cooldown_manager.is_in_cooldown(symbol, config.profit_cooldown_hours):
                        in_cooldown = True
                        remaining_time = cooldown_manager.format_remaining_time(symbol, config.profit_cooldown_hours)
                        cooldown_info = f" (冷却中: {remaining_time})"
                except Exception:
                    pass
                
                md_content += f"- **{symbol}**: 1H变化 {pct*100:.2f}%, 4H变化 {pct_4h*100:.2f}%, 信号: {signal_text}{cooldown_info}\n"
        else:
            md_content += "暂无信号\n"
            
        return self.send_message({"title": "策略状态通知", "content": md_content}, "markdown")
        
    def send_trade_notification(self, trade_type, symbol, price, qty, profit=None):
        """
        发送交易通知
        :param trade_type: 交易类型 (buy/sell)
        :param symbol: 交易对
        :param price: 价格
        :param qty: 数量
        :param profit: 利润（如果是卖出订单）
        """
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建Markdown消息
        if trade_type.lower() == 'buy':
            title = f"买入订单执行通知"
            md_content = f"### 买入订单执行通知 - {now}\n\n"
            md_content += f"- **交易对**: {symbol}\n"
            md_content += f"- **价格**: {price}\n"
            md_content += f"- **数量**: {qty}\n"
            md_content += f"- **金额**: {price * qty:.2f} USDT\n"
        else:
            title = f"卖出订单执行通知"
            md_content = f"### 卖出订单执行通知 - {now}\n\n"
            md_content += f"- **交易对**: {symbol}\n"
            md_content += f"- **价格**: {price}\n"
            md_content += f"- **数量**: {qty}\n"
            md_content += f"- **金额**: {price * qty:.2f} USDT\n"
            
            if profit is not None:
                profit_status = "盈利" if profit >= 0 else "亏损"
                md_content += f"- **{profit_status}**: {profit:.2f} USDT\n"
        
        return self.send_message({"title": title, "content": md_content}, "markdown")
        
    def send_error_notification(self, error_msg, error_trace=None):
        """
        发送错误通知
        :param error_msg: 错误消息
        :param error_trace: 错误堆栈
        """
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建Markdown消息
        md_content = f"### 策略错误通知 - {now}\n\n"
        md_content += f"#### 错误信息\n"
        md_content += f"{error_msg}\n\n"
        
        if error_trace:
            md_content += f"#### 错误堆栈\n"
            md_content += f"```\n{error_trace}\n```\n"
            
        return self.send_message({"title": "策略错误通知", "content": md_content}, "markdown") 